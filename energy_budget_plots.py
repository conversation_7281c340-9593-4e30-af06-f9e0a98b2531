#!/usr/bin/env python3
"""
Energy Budget Visualization Script
Creates comprehensive plots for energy budget analysis
"""

import numpy as np
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from energy_budget_analysis import EnergyBudgetAnalyzer
import xarray as xr

def create_energy_budget_plots(results):
    """Create comprehensive energy budget plots"""
    
    toa_budget = results['toa_budget']
    surface_budget = results['surface_budget']
    cloud_effects = results['cloud_effects']
    data = results['data']
    
    # Set up the plotting style
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 10
    
    # 1. Time series of global energy balance components
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Global Energy Budget Time Series (Years 30-60)', fontsize=16, fontweight='bold')
    
    # TOA components
    ax1 = axes[0, 0]
    toa_budget['incoming_solar'].plot(ax=ax1, label='Incoming Solar', color='orange')
    toa_budget['outgoing_solar'].plot(ax=ax1, label='Outgoing Solar', color='red')
    toa_budget['outgoing_longwave'].plot(ax=ax1, label='Outgoing LW', color='blue')
    ax1.set_title('TOA Radiation Components')
    ax1.set_ylabel('Flux (W/m²)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # TOA net balance
    ax2 = axes[0, 1]
    toa_budget['net_toa_radiation'].plot(ax=ax2, color='black', linewidth=2)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax2.set_title('Net TOA Radiation Balance')
    ax2.set_ylabel('Net Flux (W/m²)')
    ax2.grid(True, alpha=0.3)
    
    # Surface energy components
    ax3 = axes[1, 0]
    surface_budget['net_solar_surface'].plot(ax=ax3, label='Net Solar', color='orange')
    surface_budget['net_longwave_surface'].plot(ax=ax3, label='Net LW', color='blue')
    surface_budget['sensible_heat'].plot(ax=ax3, label='Sensible Heat', color='red')
    surface_budget['latent_heat'].plot(ax=ax3, label='Latent Heat', color='green')
    ax3.set_title('Surface Energy Components')
    ax3.set_ylabel('Flux (W/m²)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Cloud radiative effects
    ax4 = axes[1, 1]
    cloud_effects['shortwave_crf'].plot(ax=ax4, label='SW CRF', color='orange')
    cloud_effects['longwave_crf'].plot(ax=ax4, label='LW CRF', color='blue')
    cloud_effects['net_crf'].plot(ax=ax4, label='Net CRF', color='black', linewidth=2)
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax4.set_title('Cloud Radiative Forcing')
    ax4.set_ylabel('CRF (W/m²)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('energy_budget_timeseries.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. Spatial maps of time-averaged energy budget components
    # Calculate time means for spatial plotting
    data_mean = data.mean(dim='time')
    
    # Create spatial plots
    fig = plt.figure(figsize=(16, 12))
    
    # Define the variables to plot
    plot_vars = [
        ('FSNTOA', 'Net Solar TOA', 'W/m²', 'RdYlBu_r'),
        ('FLNT', 'Net LW TOA', 'W/m²', 'RdBu'),
        ('FSNS', 'Net Solar Surface', 'W/m²', 'RdYlBu_r'),
        ('FLNS', 'Net LW Surface', 'W/m²', 'RdBu'),
        ('SWCF', 'SW Cloud Forcing', 'W/m²', 'RdBu_r'),
        ('LWCF', 'LW Cloud Forcing', 'W/m²', 'RdBu')
    ]
    
    for i, (var, title, units, cmap) in enumerate(plot_vars):
        ax = plt.subplot(3, 2, i+1, projection=ccrs.PlateCarree())
        
        # Plot the data
        im = ax.contourf(data_mean.lon, data_mean.lat, data_mean[var], 
                        levels=20, cmap=cmap, transform=ccrs.PlateCarree())
        
        # Add map features
        ax.add_feature(cfeature.COASTLINE, linewidth=0.5)
        ax.add_feature(cfeature.BORDERS, linewidth=0.3)
        ax.set_global()
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, orientation='horizontal', pad=0.05, shrink=0.8)
        cbar.set_label(f'{units}')
        
        ax.set_title(f'{title} (30-year mean)', fontsize=12, fontweight='bold')
    
    plt.suptitle('Spatial Distribution of Energy Budget Components', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('energy_budget_spatial.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 3. Energy balance summary plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # TOA energy balance bar chart
    toa_components = {
        'Incoming Solar': float(toa_budget['incoming_solar'].mean()),
        'Outgoing Solar': -float(toa_budget['outgoing_solar'].mean()),
        'Outgoing LW': -float(toa_budget['outgoing_longwave'].mean()),
        'Net Balance': float(toa_budget['net_toa_radiation'].mean())
    }
    
    colors = ['orange', 'red', 'blue', 'black']
    bars1 = ax1.bar(range(len(toa_components)), list(toa_components.values()), color=colors)
    ax1.set_xticks(range(len(toa_components)))
    ax1.set_xticklabels(list(toa_components.keys()), rotation=45, ha='right')
    ax1.set_ylabel('Flux (W/m²)')
    ax1.set_title('TOA Energy Balance Components')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=0, color='black', linestyle='-', linewidth=0.8)
    
    # Add value labels on bars
    for bar, value in zip(bars1, toa_components.values()):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + (5 if height > 0 else -15),
                f'{value:.1f}', ha='center', va='bottom' if height > 0 else 'top')
    
    # Surface energy balance bar chart
    surface_components = {
        'Net Solar': float(surface_budget['net_solar_surface'].mean()),
        'Net LW': float(surface_budget['net_longwave_surface'].mean()),
        'Sensible': -float(surface_budget['sensible_heat'].mean()),
        'Latent': -float(surface_budget['latent_heat'].mean()),
        'Net Balance': float(surface_budget['surface_energy_balance'].mean())
    }
    
    colors2 = ['orange', 'blue', 'red', 'green', 'black']
    bars2 = ax2.bar(range(len(surface_components)), list(surface_components.values()), color=colors2)
    ax2.set_xticks(range(len(surface_components)))
    ax2.set_xticklabels(list(surface_components.keys()), rotation=45, ha='right')
    ax2.set_ylabel('Flux (W/m²)')
    ax2.set_title('Surface Energy Balance Components')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=0.8)
    
    # Add value labels on bars
    for bar, value in zip(bars2, surface_components.values()):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + (2 if height > 0 else -8),
                f'{value:.1f}', ha='center', va='bottom' if height > 0 else 'top')
    
    plt.tight_layout()
    plt.savefig('energy_balance_summary.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 4. Cloud effects analysis
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('Cloud Radiative Effects Analysis', fontsize=16, fontweight='bold')
    
    # Time series of cloud fraction and CRF
    ax1 = axes[0, 0]
    cloud_effects['cloud_fraction'].plot(ax=ax1, color='gray')
    ax1.set_title('Global Mean Cloud Fraction')
    ax1.set_ylabel('Cloud Fraction')
    ax1.grid(True, alpha=0.3)
    
    # CRF components time series
    ax2 = axes[0, 1]
    cloud_effects['shortwave_crf'].plot(ax=ax2, label='SW CRF', color='orange')
    cloud_effects['longwave_crf'].plot(ax=ax2, label='LW CRF', color='blue')
    cloud_effects['net_crf'].plot(ax=ax2, label='Net CRF', color='black', linewidth=2)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax2.set_title('Cloud Radiative Forcing Components')
    ax2.set_ylabel('CRF (W/m²)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Spatial distribution of cloud fraction
    ax3 = axes[1, 0]
    ax3 = plt.subplot(2, 2, 3, projection=ccrs.PlateCarree())
    im3 = ax3.contourf(data_mean.lon, data_mean.lat, data_mean['CLDTOT'], 
                      levels=20, cmap='Blues', transform=ccrs.PlateCarree())
    ax3.add_feature(cfeature.COASTLINE, linewidth=0.5)
    ax3.set_global()
    plt.colorbar(im3, ax=ax3, orientation='horizontal', pad=0.05, shrink=0.8, label='Cloud Fraction')
    ax3.set_title('Mean Cloud Fraction')
    
    # Spatial distribution of net CRF
    ax4 = axes[1, 1]
    ax4 = plt.subplot(2, 2, 4, projection=ccrs.PlateCarree())
    net_crf_spatial = data_mean['SWCF'] + data_mean['LWCF']
    im4 = ax4.contourf(data_mean.lon, data_mean.lat, net_crf_spatial, 
                      levels=20, cmap='RdBu_r', transform=ccrs.PlateCarree())
    ax4.add_feature(cfeature.COASTLINE, linewidth=0.5)
    ax4.set_global()
    plt.colorbar(im4, ax=ax4, orientation='horizontal', pad=0.05, shrink=0.8, label='Net CRF (W/m²)')
    ax4.set_title('Mean Net Cloud Radiative Forcing')
    
    plt.tight_layout()
    plt.savefig('cloud_effects_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """Main plotting function"""
    print("Creating energy budget visualizations...")
    
    # Run the analysis first
    from energy_budget_analysis import main as run_analysis
    results = run_analysis()
    
    # Create plots
    create_energy_budget_plots(results)
    
    print("All plots have been saved as PNG files:")
    print("- energy_budget_timeseries.png")
    print("- energy_budget_spatial.png") 
    print("- energy_balance_summary.png")
    print("- cloud_effects_analysis.png")

if __name__ == "__main__":
    main()
