# Energy Budget Sign Corrections Summary

## Critical Sign Errors Found and Fixed

### 1. **Surface Net Radiation (Line 107)**

**WRONG:**
```python
net_surface_rad = data['FSNS'] - data['FLNS']  # INCORRECT!
```

**CORRECTED:**
```python
net_surface_rad = data['FSNS'] + data['FLNS']  # CORRECT!
```

**Explanation:**
- `FSNS` = Net solar flux at surface (positive downward)
- `FLNS` = Net longwave flux at surface (typically negative, meaning net upward)
- Surface net radiation = Solar in + Longwave net = FSNS + FLNS

### 2. **Energy Conservation Equation (Line 131)**

**WRONG:**
```python
energy_residual = R_toa + R_sfc - SH - LH  # INCORRECT!
```

**CORRECTED:**
```python
energy_residual = R_toa - R_sfc - SH - LH  # CORRECT!
```

**Explanation:**
- The fundamental energy conservation equation is: **R_TOA - R_sfc - SH - LH = 0**
- This represents: Energy into Earth system - Energy into surface - Energy from surface to atmosphere = 0

### 3. **TOA Net Radiation Calculation (Line 92)**

**POTENTIALLY WRONG:**
```python
net_toa_alt = data['FSNT'] - data['FLNT']  # Check variable name
```

**CORRECTED:**
```python
net_toa_alt = data['FSNTOA'] - data['FLNT']  # Use FSNTOA for consistency
```

**Explanation:**
- `FSNTOA` = Net solar flux at top of atmosphere
- `FSNT` = Net solar flux at top of model (may be different level)
- For TOA energy budget, use FSNTOA for consistency

## Energy Budget Equation Explanation

The correct energy conservation equation is:

**R_TOA - R_sfc - SH - LH = 0**

Where:
- **R_TOA** = Net radiation at top of atmosphere (positive = energy into Earth system)
- **R_sfc** = Net radiation at surface (positive = energy into surface)  
- **SH** = Sensible heat flux (positive = energy from surface to atmosphere)
- **LH** = Latent heat flux (positive = energy from surface to atmosphere)

### Physical Interpretation:

1. **R_TOA**: Energy entering the Earth system from space
2. **R_sfc**: Energy absorbed by the surface
3. **SH + LH**: Energy transferred from surface to atmosphere

The equation states that energy entering the Earth system equals energy absorbed by surface plus energy transferred to atmosphere.

## Variable Sign Conventions

### TOA Fluxes:
- `SOLIN`: Incoming solar radiation (positive)
- `FSNTOA`: Net solar at TOA (positive = absorbed)
- `FLNT`: Net longwave at TOA (positive = upward/cooling)
- `FSUTOA`: Upwelling solar at TOA (positive = reflected)
- `FLUT`: Upwelling longwave at TOA (positive = emitted)

### Surface Fluxes:
- `FSNS`: Net solar at surface (positive = absorbed)
- `FLNS`: Net longwave at surface (typically negative = net upward)
- `SHFLX`: Sensible heat flux (positive = upward from surface)
- `LHFLX`: Latent heat flux (positive = upward from surface)

## Verification Steps

After corrections, verify that:

1. **Energy residual is small**: |R_TOA - R_sfc - SH - LH| < 1-5 W/m²
2. **Typical values make sense**:
   - R_TOA ≈ 0-2 W/m² (small imbalance)
   - R_sfc ≈ 100-150 W/m² (net surface heating)
   - SH ≈ 15-25 W/m² (sensible heat)
   - LH ≈ 80-100 W/m² (latent heat)

3. **Global energy balance**: SH + LH ≈ R_sfc (surface energy goes to atmosphere)

## Impact of Corrections

The sign corrections will:
1. **Fix energy conservation check** - residual should now be close to zero
2. **Correct surface energy budget** - proper calculation of net surface radiation
3. **Ensure physical consistency** - energy flows will make physical sense
4. **Enable proper analysis** - atmospheric heating calculations will be correct

These corrections are essential for accurate climate energy budget analysis!
