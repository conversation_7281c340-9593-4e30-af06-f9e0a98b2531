#!/usr/bin/env python3
"""
Energy Budget Analysis for E2000CO2 Climate Model Data
Analyzes energy budget components using SOM data from years 40-100
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import pandas as pd
import glob
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class EnergyBudgetAnalyzer:
    def __init__(self, data_path="/data/anu_x1/Usha_data/E2000CO2"):
        self.data_path = data_path
        self.years = range(40, 101)  # Years 30-60
        self.energy_vars = {
            # Top of Atmosphere (TOA) fluxes
            'FSNTOA': 'Net solar flux at top of atmosphere',
            'FSNT' : 'Net solar flux at top of model',
            'FSNTOAC': 'Clearsky net solar flux at top of atmosphere', 
            'FLNT': 'Net longwave flux at top of model',
            'FLNTC': 'Clearsky net longwave flux at top of model',
            'FSUTOA': 'Upwelling solar flux at top of atmosphere',
            'FLUT': 'Upwelling longwave flux at top of model',
            'FLUTC': 'Clearsky upwelling longwave flux at top of model',
            'SOLIN': 'Solar insolation',
            
            # Surface fluxes
            'FSNS': 'Net solar flux at surface',
            'FSNSC': 'Clearsky net solar flux at surface',
            'FLNS': 'Net longwave flux at surface',
            'FLNSC': 'Clearsky net longwave flux at surface',
            'FSDS': 'Downwelling solar flux at surface',
            'FSDSC': 'Clearsky downwelling solar flux at surface',
            'FLDS': 'Downwelling longwave flux at surface',
            'FLDSC': 'Clearsky downwelling longwave flux at surface',
            'SHFLX': 'Surface sensible heat flux',
            'LHFLX': 'Surface latent heat flux',
            
            # Cloud forcing
            'SWCF': 'Shortwave cloud forcing',
            'LWCF': 'Longwave cloud forcing',
            
            # Temperature and other variables
            'TS': 'Surface temperature (radiative)',
            'TREFHT': 'Reference height temperature',
            'CLDTOT': 'Vertically-integrated total cloud'
        }
        
    def get_file_list(self):
        """Get list of files for years 40-100"""
        files = []
        for year in self.years:
            pattern = f"{self.data_path}/E2000C5_co2.cam.h0.{year:04d}-*.nc"
            year_files = sorted(glob.glob(pattern))
            files.extend(year_files)
        return files
    
    def load_data(self, variables=None):
        """Load and concatenate data from multiple files"""
        if variables is None:
            variables = list(self.energy_vars.keys())
            
        files = self.get_file_list()
        print(f"Loading {len(files)} files from years {self.years[0]}-{self.years[-1]}")
        
        # Load data
        datasets = []
        for file in files:
            try:
                ds = xr.open_dataset(file)[variables]
                datasets.append(ds)
            except Exception as e:
                print(f"Error loading {file}: {e}")
                continue
                
        # Concatenate along time dimension
        data = xr.concat(datasets, dim='time')
        return data
    
    def calculate_toa_energy_balance(self, data):
        """Calculate Top of Atmosphere energy balance"""
        # Net radiation at TOA = Incoming solar - Outgoing solar - Outgoing longwave
        net_toa = data['SOLIN'] - data['FSUTOA'] - data['FLUT']
        
        # Alternative calculation using net fluxes
        net_toa_alt = data['FSNT'] - data['FLNT']
        
        return {
            'net_toa_radiation': net_toa,
            'net_toa_alternative': net_toa_alt,
            'incoming_solar': data['SOLIN'],
            'outgoing_solar': data['FSUTOA'], 
            'outgoing_longwave': data['FLUT'],
            'net_solar_toa': data['FSNTOA'],
            'net_longwave_toa': data['FLNT']
        }
    
    def calculate_surface_energy_balance(self, data):
        """Calculate surface energy balance"""
        # Net radiation at surface = Net solar + Net longwave
        net_surface_rad = data['FSNS'] - data['FLNS']
        # Surface energy balance: Net radiation - Sensible - Latent = 0 (ideally)
        surface_energy_balance = net_surface_rad - data['SHFLX'] - data['LHFLX']

        return {
            'net_surface_radiation': net_surface_rad,
            'surface_energy_balance': surface_energy_balance,
            'net_solar_surface': data['FSNS'],
            'net_longwave_surface': data['FLNS'],
            'sensible_heat': data['SHFLX'],
            'latent_heat': data['LHFLX']
        }

    def calculate_atmospheric_energy_budget(self, toa_budget, surface_budget):
        """Calculate atmospheric energy budget and check energy conservation"""
        # Atmospheric energy budget: R_TOA - R_sfc - SH - LH = 0
        # This represents the net energy storage/transport in the atmosphere

        R_toa = toa_budget['net_toa_alternative']  # Net radiation at TOA
        R_sfc = surface_budget['net_surface_radiation']  # Net radiation at surface
        SH = surface_budget['sensible_heat']  # Sensible heat flux
        LH = surface_budget['latent_heat']  # Latent heat flux

        # Energy conservation check
        energy_residual = R_toa + R_sfc - SH - LH

        # Atmospheric energy budget components
        atmospheric_heating = R_sfc + SH + LH - R_toa  # Net atmospheric heating

        return {
            'energy_residual': energy_residual,
            'atmospheric_heating': atmospheric_heating,
            'toa_net': R_toa,
            'surface_net': R_sfc,
            'sensible_flux': SH,
            'latent_flux': LH
        }
    
    def calculate_cloud_effects(self, data):
        """Calculate cloud radiative effects"""
        return {
            'shortwave_crf': data['SWCF'],  # Already calculated in model
            'longwave_crf': data['LWCF'],   # Already calculated in model
            'net_crf': data['SWCF'] + data['LWCF'],
            'cloud_fraction': data['CLDTOT']
        }
    
    def calculate_global_means(self, data_dict, weights=None):
        """Calculate area-weighted global means"""
        if weights is None:
            # Calculate area weights based on latitude
            lat = data_dict[list(data_dict.keys())[0]].lat
            weights = np.cos(np.deg2rad(lat))
            
        global_means = {}
        for key, data_array in data_dict.items():
            if len(data_array.dims) >= 2 and 'lat' in data_array.dims and 'lon' in data_array.dims:
                global_mean = data_array.weighted(weights).mean(dim=['lat', 'lon'])
                global_means[key] = global_mean
            else:
                global_means[key] = data_array
                
        return global_means

def main():
    """Main analysis function"""
    print("Starting Energy Budget Analysis for E2000CO2 data (Years 40-100)")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = EnergyBudgetAnalyzer()
    
    # Load data
    print("Loading data...")
    data = analyzer.load_data()
    
    # Calculate energy budget components
    print("Calculating energy budget components...")
    toa_budget = analyzer.calculate_toa_energy_balance(data)
    surface_budget = analyzer.calculate_surface_energy_balance(data)
    cloud_effects = analyzer.calculate_cloud_effects(data)

    # Calculate atmospheric energy budget and conservation check
    print("Calculating atmospheric energy budget...")
    atm_budget = analyzer.calculate_atmospheric_energy_budget(toa_budget, surface_budget)

    # Calculate global means
    print("Calculating global means...")
    lat_weights = np.cos(np.deg2rad(data.lat))

    toa_global = analyzer.calculate_global_means(toa_budget, lat_weights)
    surface_global = analyzer.calculate_global_means(surface_budget, lat_weights)
    cloud_global = analyzer.calculate_global_means(cloud_effects, lat_weights)
    atm_global = analyzer.calculate_global_means(atm_budget, lat_weights)
    
    # Calculate time means
    print("Calculating time-averaged statistics...")
    
    # TOA energy balance statistics
    print("\n" + "="*50)
    print("TOP OF ATMOSPHERE ENERGY BALANCE")
    print("="*50)
    
    for key, desc in [
        ('incoming_solar', 'Incoming Solar Radiation'),
        ('outgoing_solar', 'Outgoing Solar Radiation'), 
        ('outgoing_longwave', 'Outgoing Longwave Radiation'),
        ('net_toa_radiation', 'Net TOA Radiation Balance'),
        ('net_solar_toa', 'Net Solar at TOA'),
        ('net_longwave_toa', 'Net Longwave at TOA')
    ]:
        if key in toa_global:
            mean_val = float(toa_global[key].mean())
            std_val = float(toa_global[key].std())
            print(f"{desc:.<35} {mean_val:>8.2f} ± {std_val:>6.2f} W/m²")
    
    # Surface energy balance statistics  
    print("\n" + "="*50)
    print("SURFACE ENERGY BALANCE")
    print("="*50)
    
    for key, desc in [
        ('net_solar_surface', 'Net Solar at Surface'),
        ('net_longwave_surface', 'Net Longwave at Surface'),
        ('sensible_heat', 'Sensible Heat Flux'),
        ('latent_heat', 'Latent Heat Flux'),
        ('net_surface_radiation', 'Net Surface Radiation'),
        ('surface_energy_balance', 'Surface Energy Balance')
    ]:
        if key in surface_global:
            mean_val = float(surface_global[key].mean())
            std_val = float(surface_global[key].std())
            print(f"{desc:.<35} {mean_val:>8.2f} ± {std_val:>6.2f} W/m²")
    
    # Cloud radiative effects
    print("\n" + "="*50)
    print("CLOUD RADIATIVE EFFECTS")
    print("="*50)

    for key, desc in [
        ('shortwave_crf', 'Shortwave Cloud Forcing'),
        ('longwave_crf', 'Longwave Cloud Forcing'),
        ('net_crf', 'Net Cloud Forcing'),
        ('cloud_fraction', 'Total Cloud Fraction')
    ]:
        if key in cloud_global:
            mean_val = float(cloud_global[key].mean())
            std_val = float(cloud_global[key].std())
            unit = "W/m²" if 'crf' in key or 'forcing' in key.lower() else "fraction"
            print(f"{desc:.<35} {mean_val:>8.3f} ± {std_val:>6.3f} {unit}")

    # Atmospheric energy budget and conservation check
    print("\n" + "="*50)
    print("ATMOSPHERIC ENERGY BUDGET & CONSERVATION CHECK")
    print("="*50)
    print("Energy Conservation: R_TOA - R_sfc - SH - LH = 0")
    print("-"*50)

    for key, desc in [
        ('toa_net', 'Net Radiation at TOA (R_TOA)'),
        ('surface_net', 'Net Radiation at Surface (R_sfc)'),
        ('sensible_flux', 'Sensible Heat Flux (SH)'),
        ('latent_flux', 'Latent Heat Flux (LH)'),
        ('energy_residual', 'Energy Residual (should be ~0)'),
        ('atmospheric_heating', 'Net Atmospheric Heating')
    ]:
        if key in atm_global:
            mean_val = float(atm_global[key].mean())
            std_val = float(atm_global[key].std())
            print(f"{desc:.<35} {mean_val:>8.2f} ± {std_val:>6.2f} W/m²")

    # Check energy conservation
    residual_mean = float(atm_global['energy_residual'].mean())
    residual_std = float(atm_global['energy_residual'].std())
    print(f"\nEnergy Conservation Check:")
    print(f"Mean residual: {residual_mean:.3f} ± {residual_std:.3f} W/m²")
    if abs(residual_mean) < 1.0:
        print("✓ Energy is well conserved (residual < 1 W/m²)")
    elif abs(residual_mean) < 5.0:
        print("⚠ Energy conservation is reasonable (residual < 5 W/m²)")
    else:
        print("✗ Energy conservation issue detected (residual > 5 W/m²)")

    return {
        'toa_budget': toa_global,
        'surface_budget': surface_global,
        'cloud_effects': cloud_global,
        'atmospheric_budget': atm_global,
        'data': data
    }

if __name__ == "__main__":
    results = main()
    print(f"\nAnalysis complete! Results stored in 'results' dictionary.")
