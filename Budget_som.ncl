


f1 = systemfunc("ls /data/anu_x1/Usha_data/E2000CO2/E2000C5_co2.cam.h0.0*.nc")
f1a = reshape(f1,(/100,12/))
files1 = addfiles(f1a(40:99,:),"r") ; season select

;f2 = systemfunc("ls /user1/usha/Alt_expdata/E20002CO2/E2000C5_2co2.cam.h0.0*.nc")
;f2a = reshape(f2,(/100,12/))
;files2 = addfiles(f2a(40:99,:),"r") ; season sele

f3 = addfile("//data/anu_x1/Usha_data/E2000CO2/E2000C5_co2.clm2.h0.0001-01.nc", "r");

lat = files1[0]->lat
lon = files1[0]->lon
gw   = files1[0]->gw
maskl = f3->landmask

; TOA RF
FSNT_co2 = files1[:]->FSNT;(0:1199,:,:)
FLNT_co2 = files1[:]->FLNT;(0:1199,:,:)

R_TOA=FSNT_co2 - FLNT_co2
R_TOA_mean = dim_avg_n_Wrap(R_TOA, 0)
R_TOA_mean_gw=wgt_areaave_Wrap(R_TOA_mean, gw, 1, 0)
print(R_TOA_mean_gw)

;Surface RF
FSNS_co2 = files1[:]->FSNS;(0:1199,:,:)
FLNS_co2 = files1[:]->FLNS;(0:1199,:,:)

R_sfc=FSNS_co2 - FLNS_co2
R_sfc_mean= dim_avg_n_Wrap(R_sfc, 0)
R_sfc_mean_gw=wgt_areaave_Wrap(R_sfc_mean, gw, 1, 0)
print(R_sfc_mean_gw)
;SHFLX
SHFLX_co2 = files1[:]->SHFLX;(0:1199,:,:)
SHFLX_co2_mean= dim_avg_n_Wrap(SHFLX_co2, 0)
printVarSummary(SHFLX_co2_mean)
SHFLX_co2_mean_gw=wgt_areaave_Wrap(SHFLX_co2_mean, gw, 1, 0)
print(SHFLX_co2_mean_gw)

;LHFLX
LHFLX_co2 = files1[:]->LHFLX;(0:1199,:,:)
LHFLX_co2_mean= dim_avg_n_Wrap(LHFLX_co2, 0)
printVarSummary(LHFLX_co2_mean)
LHFLX_co2_mean_gw=wgt_areaave_Wrap(LHFLX_co2_mean, gw, 1, 0)
print(LHFLX_co2_mean_gw)
;Net atmospheric heating
atmospheric_heating = R_sfc + SHFLX_co2 + LHFLX_co2 - R_TOA

copy_VarCoords(atmospheric_heating ,FSNS_co2)
budget_avg= dim_avg_n_Wrap(atmospheric_heating, 0)


;----Calculate global mean as well
budget_avg_gmean = wgt_areaave_Wrap(budget_avg,gw,1,0)  ; global area weighted avg


dims = dimsizes(budget_avg_gmean)
nlat = dims(0)
nlon  = dims(1)


 print(budget_avg_gmean)




