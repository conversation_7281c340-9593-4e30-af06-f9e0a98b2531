#!/usr/bin/env python3
"""
Energy Budget Analysis with Correct Sign Conventions
Implements: R_TOA - R_sfc - SH - LH = 0
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import pandas as pd
import glob
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class EnergyBudgetAnalyzer:
    def __init__(self, data_path="/data/anu_x1/Usha_data/F2000CO2"):
        self.data_path = data_path
        self.years = range(30, 61)  # Years 30-60
        self.energy_vars = {
            # Top of Atmosphere (TOA) fluxes
            'FSNTOA': 'Net solar flux at top of atmosphere',
            'FLNT': 'Net longwave flux at top of model',
            'FSUTOA': 'Upwelling solar flux at top of atmosphere',
            'FLUT': 'Upwelling longwave flux at top of model',
            'SOLIN': 'Solar insolation',
            
            # Surface fluxes
            'FSNS': 'Net solar flux at surface',
            'FLNS': 'Net longwave flux at surface',
            'SHFLX': 'Surface sensible heat flux',
            'LHFLX': 'Surface latent heat flux',
            
            # Cloud forcing
            'SWCF': 'Shortwave cloud forcing',
            'LWCF': 'Longwave cloud forcing',
            
            # Temperature and other variables
            'TS': 'Surface temperature (radiative)',
            'CLDTOT': 'Vertically-integrated total cloud'
        }
        
    def get_file_list(self):
        """Get list of files for years 30-60"""
        files = []
        for year in self.years:
            pattern = f"{self.data_path}/F2000C5_co2.cam.h0.{year:04d}-*.nc"
            year_files = sorted(glob.glob(pattern))
            files.extend(year_files)
        return files
    
    def load_sample_data(self):
        """Load a sample of data files to test the analysis"""
        files = self.get_file_list()
        print(f"Found {len(files)} files from years {self.years[0]}-{self.years[-1]}")
        
        # Load just a few files for testing
        sample_files = files[:12]  # First year of data
        print(f"Loading sample: {len(sample_files)} files")
        
        datasets = []
        for file in sample_files:
            try:
                # Try to load with scipy backend first
                ds = xr.open_dataset(file, engine='scipy')
                # Select only the variables we need
                available_vars = [var for var in self.energy_vars.keys() if var in ds.variables]
                if available_vars:
                    ds_subset = ds[available_vars]
                    datasets.append(ds_subset)
                    print(f"Loaded: {os.path.basename(file)} with {len(available_vars)} variables")
                ds.close()
            except Exception as e:
                print(f"Error loading {file}: {e}")
                continue
                
        if not datasets:
            raise ValueError("No data could be loaded!")
            
        # Concatenate along time dimension
        data = xr.concat(datasets, dim='time')
        return data
    
    def calculate_energy_budget_correct_signs(self, data):
        """Calculate energy budget with correct sign conventions
        
        Energy Conservation: R_TOA - R_sfc - SH - LH = 0
        
        Sign conventions:
        - R_TOA: Net radiation at TOA (positive = energy into Earth system)
        - R_sfc: Net radiation at surface (positive = energy into surface)
        - SH: Sensible heat flux (positive = energy from surface to atmosphere)
        - LH: Latent heat flux (positive = energy from surface to atmosphere)
        """
        
        # TOA net radiation (positive = energy into Earth system)
        # R_TOA = Net solar in - Net longwave out
        R_TOA = data['FSNTOA'] - data['FLNT']
        
        # Surface net radiation (positive = energy into surface)
        # R_sfc = Net solar in - Net longwave out (note: FLNS is typically negative)
        R_sfc = data['FSNS'] - data['FLNS']
        
        # Surface fluxes (positive = energy from surface to atmosphere)
        SH = data['SHFLX']  # Sensible heat flux
        LH = data['LHFLX']  # Latent heat flux
        
        # Energy conservation check: R_TOA - R_sfc - SH - LH = 0
        energy_residual = R_TOA - R_sfc - SH - LH
        
        # Atmospheric energy budget
        # Net atmospheric heating = Energy absorbed by atmosphere
        # = (Energy from TOA) - (Energy to surface) + (Energy from surface fluxes)
        atmospheric_heating = -R_TOA + R_sfc + SH + LH
        
        return {
            'R_TOA': R_TOA,
            'R_sfc': R_sfc, 
            'SH': SH,
            'LH': LH,
            'energy_residual': energy_residual,
            'atmospheric_heating': atmospheric_heating,
            
            # Individual components for analysis
            'net_solar_toa': data['FSNTOA'],
            'net_longwave_toa': data['FLNT'],
            'net_solar_surface': data['FSNS'],
            'net_longwave_surface': data['FLNS'],
            'incoming_solar': data['SOLIN'],
            'outgoing_solar': data['FSUTOA'],
            'outgoing_longwave': data['FLUT']
        }
    
    def calculate_cloud_effects(self, data):
        """Calculate cloud radiative effects"""
        return {
            'shortwave_crf': data['SWCF'],
            'longwave_crf': data['LWCF'],
            'net_crf': data['SWCF'] + data['LWCF'],
            'cloud_fraction': data['CLDTOT']
        }
    
    def calculate_global_means(self, data_dict):
        """Calculate area-weighted global means"""
        # Get latitude for area weighting
        lat = data_dict[list(data_dict.keys())[0]].lat
        weights = np.cos(np.deg2rad(lat))
        
        global_means = {}
        for key, data_array in data_dict.items():
            if len(data_array.dims) >= 2 and 'lat' in data_array.dims and 'lon' in data_array.dims:
                global_mean = data_array.weighted(weights).mean(dim=['lat', 'lon'])
                global_means[key] = global_mean
            else:
                global_means[key] = data_array
                
        return global_means

def main():
    """Main analysis function"""
    print("Energy Budget Analysis with Correct Sign Conventions")
    print("=" * 60)
    print("Implementing: R_TOA - R_sfc - SH - LH = 0")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = EnergyBudgetAnalyzer()
    
    # Load sample data
    print("Loading sample data...")
    try:
        data = analyzer.load_sample_data()
        print(f"Data loaded successfully: {data.dims}")
        print(f"Available variables: {list(data.variables)}")
    except Exception as e:
        print(f"Error loading data: {e}")
        return None
    
    # Calculate energy budget components
    print("\nCalculating energy budget components...")
    energy_budget = analyzer.calculate_energy_budget_correct_signs(data)
    cloud_effects = analyzer.calculate_cloud_effects(data)
    
    # Calculate global means
    print("Calculating global means...")
    energy_global = analyzer.calculate_global_means(energy_budget)
    cloud_global = analyzer.calculate_global_means(cloud_effects)
    
    # Print results
    print("\n" + "="*60)
    print("ENERGY BUDGET ANALYSIS RESULTS")
    print("="*60)
    print("Energy Conservation Equation: R_TOA - R_sfc - SH - LH = 0")
    print("-"*60)
    
    # Main energy budget components
    for key, desc in [
        ('R_TOA', 'Net Radiation at TOA'),
        ('R_sfc', 'Net Radiation at Surface'),
        ('SH', 'Sensible Heat Flux'),
        ('LH', 'Latent Heat Flux'),
        ('energy_residual', 'Energy Residual (should be ~0)'),
        ('atmospheric_heating', 'Net Atmospheric Heating')
    ]:
        if key in energy_global:
            mean_val = float(energy_global[key].mean())
            std_val = float(energy_global[key].std())
            print(f"{desc:.<35} {mean_val:>8.2f} ± {std_val:>6.2f} W/m²")
    
    # Detailed flux components
    print("\n" + "="*50)
    print("DETAILED FLUX COMPONENTS")
    print("="*50)
    
    for key, desc in [
        ('incoming_solar', 'Incoming Solar (SOLIN)'),
        ('net_solar_toa', 'Net Solar at TOA (FSNTOA)'),
        ('net_longwave_toa', 'Net Longwave at TOA (FLNT)'),
        ('outgoing_solar', 'Outgoing Solar (FSUTOA)'),
        ('outgoing_longwave', 'Outgoing Longwave (FLUT)'),
        ('net_solar_surface', 'Net Solar at Surface (FSNS)'),
        ('net_longwave_surface', 'Net Longwave at Surface (FLNS)')
    ]:
        if key in energy_global:
            mean_val = float(energy_global[key].mean())
            std_val = float(energy_global[key].std())
            print(f"{desc:.<40} {mean_val:>8.2f} ± {std_val:>6.2f} W/m²")
    
    # Energy conservation check
    print("\n" + "="*50)
    print("ENERGY CONSERVATION CHECK")
    print("="*50)
    
    residual_mean = float(energy_global['energy_residual'].mean())
    residual_std = float(energy_global['energy_residual'].std())
    
    print(f"Energy residual: {residual_mean:.3f} ± {residual_std:.3f} W/m²")
    
    if abs(residual_mean) < 1.0:
        print("✓ Energy is well conserved (residual < 1 W/m²)")
    elif abs(residual_mean) < 5.0:
        print("⚠ Energy conservation is reasonable (residual < 5 W/m²)")
    else:
        print("✗ Energy conservation issue detected (residual > 5 W/m²)")
    
    # Manual verification
    R_TOA = float(energy_global['R_TOA'].mean())
    R_sfc = float(energy_global['R_sfc'].mean())
    SH = float(energy_global['SH'].mean())
    LH = float(energy_global['LH'].mean())
    
    manual_residual = R_TOA - R_sfc - SH - LH
    print(f"\nManual verification:")
    print(f"R_TOA - R_sfc - SH - LH = {R_TOA:.2f} - {R_sfc:.2f} - {SH:.2f} - {LH:.2f}")
    print(f"                        = {manual_residual:.3f} W/m²")
    
    # Cloud effects
    print("\n" + "="*50)
    print("CLOUD RADIATIVE EFFECTS")
    print("="*50)
    
    for key, desc in [
        ('shortwave_crf', 'Shortwave Cloud Forcing'),
        ('longwave_crf', 'Longwave Cloud Forcing'),
        ('net_crf', 'Net Cloud Forcing'),
        ('cloud_fraction', 'Total Cloud Fraction')
    ]:
        if key in cloud_global:
            mean_val = float(cloud_global[key].mean())
            std_val = float(cloud_global[key].std())
            unit = "W/m²" if 'crf' in key or 'forcing' in key.lower() else "fraction"
            print(f"{desc:.<35} {mean_val:>8.3f} ± {std_val:>6.3f} {unit}")
    
    return {
        'energy_budget': energy_global,
        'cloud_effects': cloud_global,
        'data': data
    }

if __name__ == "__main__":
    results = main()
    if results:
        print(f"\nAnalysis complete! Results stored in 'results' dictionary.")
    else:
        print("\nAnalysis failed - check data loading issues.")
