#!/usr/bin/env python3
"""
Tropical (30S-30N) Energy Budget for CESM E2000CO2
"""

import xarray as xr
import numpy as np
import glob

# ---------------------------------------
# 1. Collect files
# ---------------------------------------
files1 = sorted(glob.glob("/data/anu_x1/Usha_data/E2000CO2/E2000C5_co2.cam.h0.0*.nc"))

# Reshape to [year, month] = (100, 12)
files1a = np.array(files1).reshape(100, 12)

# Select years 40–99 → indices 40:100
files1_sel = files1a[40:100, :].ravel().tolist()

# Open as a single dataset
ds = xr.open_mfdataset(files1_sel, combine="by_coords")

# Landmask (optional)
f3 = xr.open_dataset("/data/anu_x1/Usha_data/E2000CO2/E2000C5_co2.clm2.h0.0001-01.nc")
maskl = f3["landmask"]

# ---------------------------------------
# 2. Subset to tropics 30S-30N
# ---------------------------------------
ds_trop = ds.sel(lat=slice(-30, 30))
lat = ds_trop["lat"]
lon = ds_trop["lon"]
gw  = ds_trop["gw"]  # tropical area weights

# ---------------------------------------
# 3. Helper: area-weighted mean
# ---------------------------------------
def area_mean(var, weights):
    """Area-weighted mean over lat/lon"""
    return var.weighted(weights).mean(dim=("lat","lon"))

# ---------------------------------------
# 4. Energy budget calculations (tropics)
# ---------------------------------------
FSNT = ds_trop["FSNT"]
FLNT = ds_trop["FLNT"]
FSNS = ds_trop["FSNS"]
FLNS = ds_trop["FLNS"]
SHFLX = ds_trop["SHFLX"]
LHFLX = ds_trop["LHFLX"]

# TOA and surface net radiation
R_TOA = FSNT - FLNT
R_sfc = FSNS - FLNS

# Energy budget residual (should be ~0 for conservation)
# Energy conservation: R_TOA - R_sfc - SH - LH = 0
energy_budget_residual = R_TOA - R_sfc - SHFLX - LHFLX

# Area-weighted tropical mean (time series)
R_TOA_ts = area_mean(R_TOA, gw)
R_sfc_ts = area_mean(R_sfc, gw)
SH_ts    = area_mean(SHFLX, gw)
LH_ts    = area_mean(LHFLX, gw)
budget_ts = area_mean(energy_budget_residual, gw)

# Time mean
print("Tropical Mean TOA Net Radiation (FSNT - FLNT):", float(R_TOA_ts.mean(dim='time').values), "W/m²")
print("Tropical Mean Surface Net Radiation (FSNS - FLNS):", float(R_sfc_ts.mean(dim='time').values), "W/m²")
print("Tropical Mean SH Flux:", float(SH_ts.mean(dim='time').values), "W/m²")
print("Tropical Mean LH Flux:", float(LH_ts.mean(dim='time').values), "W/m²")
print("Tropical Energy Budget Residual:", float(budget_ts.mean(dim='time').values), "W/m²")
