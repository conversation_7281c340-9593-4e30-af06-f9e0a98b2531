#!/usr/bin/env python3
"""
Python translation of NCL energy budget script
Computes global mean TOA flux, surface flux, SH, LH, and atmospheric heating
for CESM E2000CO2 data (years 40–99)
"""

import xarray as xr
import numpy as np
import glob

# ---------------------------------------
# 1. Collect files
# ---------------------------------------
files1 = sorted(glob.glob("/data/anu_x1/Usha_data/E2000CO2/E2000C5_co2.cam.h0.0*.nc"))

# Reshape to [year, month] = (100, 12)
files1a = np.array(files1).reshape(100, 12)

# Select years 40–99 → indices 40:100 (NCL 40:99)
files1_sel = files1a[40:100, :].ravel().tolist()

# Open as a single dataset
ds = xr.open_mfdataset(files1_sel, combine="by_coords")

# Landmask file (for later use if needed)
f3 = xr.open_dataset("/data/anu_x1/Usha_data/E2000CO2/E2000C5_co2.clm2.h0.0001-01.nc")
maskl = f3["landmask"]

ds_trop = ds.sel(lat=slice(-30, 30))

lat = ds_trop["lat"]
lon = ds_trop["lon"]
gw  = ds_trop["gw"]

# ---------------------------------------
# 2. Helper: global weighted mean
# ---------------------------------------
def global_mean(var, weights):
    """Area-weighted global mean"""
    return var.weighted(weights).mean(dim=("lat", "lon"))

# ---------------------------------------
# 3. Energy budget calculations
# ---------------------------------------

# TOA net radiation
FSNT = ds["FSNT"]    # Net SW at TOA
FLNT = ds["FLNT"]    # Net LW at TOA
R_TOA = FSNT - FLNT

# Surface net radiation
FSNS = ds["FSNS"]    # Net SW at surface
FLNS = ds["FLNS"]    # Net LW at surface (typically negative)
R_sfc = FSNS + FLNS  # CORRECTED: Add FLNS since it's already net LW

# Surface turbulent fluxes
SHFLX = ds["SHFLX"]  # Sensible heat flux (upward)
LHFLX = ds["LHFLX"]  # Latent heat flux (upward)

# Energy budget residual (should be ~0 for conservation)
# Correct equation: R_TOA - R_sfc - SH - LH = 0
energy_budget_residual = R_TOA - R_sfc - SHFLX - LHFLX

# Global mean (time series)
R_TOA_gmean_ts = global_mean(R_TOA, gw)
R_sfc_gmean_ts = global_mean(R_sfc, gw)
SH_gmean_ts    = global_mean(SHFLX, gw)
LH_gmean_ts    = global_mean(LHFLX, gw)
budget_gmean_ts = global_mean(atmospheric_heating, gw)

# Then take mean over time
R_TOA_gmean = R_TOA_gmean_ts.mean(dim="time")
R_sfc_gmean = R_sfc_gmean_ts.mean(dim="time")
SH_gmean    = SH_gmean_ts.mean(dim="time")
LH_gmean    = LH_gmean_ts.mean(dim="time")
budget_gmean = budget_gmean_ts.mean(dim="time")

# Now they are scalars → can be printed
print("Global Mean TOA Net Radiation (FSNT - FLNT):", float(R_TOA_gmean.values), "W/m²")
print("Global Mean Surface Net Radiation (FSNS - FLNS):", float(R_sfc_gmean.values), "W/m²")
print("Global Mean SH Flux:", float(SH_gmean.values), "W/m²")
print("Global Mean LH Flux:", float(LH_gmean.values), "W/m²")
#print("Global Mean Net Atmospheric Heating:", float(budget_gmean.values), "W/m²")
print("Energy Budget Residual:", float(budget_gmean.values), "W/m²")