# CESM Energy Budget Sign Conventions

## CESM Variable Definitions

Based on CESM documentation, the sign conventions are:

### TOA Fluxes:
- **FSNT**: Net solar flux at top of model (positive = absorbed by Earth)
- **FLNT**: Net longwave flux at top of model (positive = emitted to space)
- **R_TOA = FSNT - FLNT** (net energy into Earth system)

### Surface Fluxes:
- **FSNS**: Net solar flux at surface (positive = absorbed by surface)
- **FLNS**: Net longwave flux at surface (positive = emitted by surface)
- **SHFLX**: Sensible heat flux (positive = upward from surface)
- **LHFLX**: Latent heat flux (positive = upward from surface)

## Surface Net Radiation in CESM

For CESM, the surface net radiation should be:
**R_sfc = FSNS - FLNS**

Where:
- FSNS > 0 (solar energy absorbed by surface)
- FLNS > 0 (longwave energy emitted by surface)
- R_sfc = net energy into surface

## Energy Conservation Equation

The energy conservation equation remains:
**R_TOA - R_sfc - SH - LH = 0**

This represents:
- Energy into Earth system (R_TOA)
- Minus energy into surface (R_sfc)  
- Minus energy from surface to atmosphere (SH + LH)
- Should equal zero for energy conservation

## Current Script Analysis

The current script has:
```python
# TOA
R_TOA = FSNT - FLNT  ✓ CORRECT

# Surface  
R_sfc = FSNS - FLNS  ✓ CORRECT for CESM

# Energy budget
energy_budget_residual = R_TOA - R_sfc - SHFLX - LHFLX  ✓ CORRECT
```

## Verification

The script appears to be **CORRECT** for CESM data with the current formulation:

1. **TOA**: R_TOA = FSNT - FLNT ✓
2. **Surface**: R_sfc = FSNS - FLNS ✓  
3. **Conservation**: R_TOA - R_sfc - SH - LH = 0 ✓

The energy budget residual should be close to zero if the model conserves energy properly.

## Expected Values for CESM

Typical global mean values should be approximately:
- R_TOA ≈ 0-2 W/m² (small climate imbalance)
- R_sfc ≈ 100-120 W/m² (net surface heating)
- SHFLX ≈ 15-25 W/m² (sensible heat)
- LHFLX ≈ 80-100 W/m² (latent heat)
- Residual ≈ 0 ± 1 W/m² (energy conservation)
